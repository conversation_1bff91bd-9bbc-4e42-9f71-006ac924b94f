.tenweb-content-wrap {
    display: flex;
    font-family: 'Open Sans', sans-serif;
    width: 940px;
    border-radius: 6px;
    overflow: hidden;
    margin: 20px 0 0 93px;
    height: 365px;
}
.tenweb-content-wrap .tenweb-left-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 30px 35px;
    width: 470px;
    box-sizing: border-box;
    background: #fff;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    position: relative;
}
.tenweb-content-wrap .tenweb-left-section > img {
    width: 72px;
    margin-bottom: 30px;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content > div {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 5px;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content img {
    width: 20px;
    margin-right: 8px;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content h3 {
    font-size: 18px;
    font-weight: 800;
    line-height: 28px;
    margin: 0;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content p {
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 25px;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content ul {
    margin: 0;
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content ul li {
    display: flex;
    align-self: flex-start;
    justify-content: flex-start;
    margin: 0;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content ul li:first-child {
    margin-bottom: 15px;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content ul li img {
    width: 18px;
    height: 18px;
    margin: 4px 10px 0 0;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content ul li span {
    font-size: 12px;
    font-weight: 600;
    line-height: 18px;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content a.logged-in {
    position: absolute;
    bottom: 0;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content a {
    width: 180px;
    height: 40px;
    background: #2a2f30;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    align-self: flex-end;
    color: #fff;
    border: none;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content a:visited {
    background: #2a2f30;
    color: #fff;
    border: none;
}
.tenweb-content-wrap .tenweb-left-section .tenweb-left-section-content a:hover {
    background: rgba(42,47,48, 0.8);
    color: #fff;
    border: none;
}
.tenweb-content-wrap .tenweb-right-banner {
    width: 470px;
    background: rgba(33, 96, 181, 0.05) url(../images/manager-image2x.png) no-repeat center;
    background-size: contain;
}
.tenweb-footer-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    background: #fff;
    border: 1px solid #EAEAEA;
    font-family: 'Open Sans', sans-serif;
    width: 940px;
    border-radius: 6px;
    padding: 14px 12px;
    margin: 10px 0 0 93px;
}
.tenweb-footer-wrap > img {
    width: 14px;
    margin-right: 8px;
}
.tenweb-footer-wrap > b {
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    margin-right: 8px;
}
.tenweb-footer-wrap > span {
    font-size: 12px;
    line-height: 18px;
    text-decoration: underline;
    cursor: pointer;
}

.tenweb_manager_notice.tenweb_menu_notice {
    display: flex;
    background: #FDF2F4 0% 0% no-repeat padding-box;
    border: 1px solid #EAEAEA;
    border-radius: 6px;
    margin: 10px 0 0 93px;
    align-items: center;
    justify-content: flex-start;
    width: 940px;
    box-sizing: border-box;
}
.tenweb_manager_notice.tenweb_menu_notice p {
    color: #2A2F30;
    font-weight: 400;
}
.tenweb_manager_notice.tenweb_menu_notice > img {
    width: 14px;
    margin-right: 8px;
}
.tenweb_manager_notice.tenweb_menu_notice p b {
    font-weight: 700;
}
.tenweb_manager_notice.tenweb_menu_notice p a {
    text-decoration: underline;
    font-weight: 600;
    color: #2A2F30;
}
.tenweb_manager_notice.tenweb_menu_notice p a:hover{
    text-decoration: none;
    color: #2A2F30;
}
.tenweb_manager_notice.tenweb_menu_notice p a:active {
    color: #2A2F30;
    text-decoration: underline;
}
