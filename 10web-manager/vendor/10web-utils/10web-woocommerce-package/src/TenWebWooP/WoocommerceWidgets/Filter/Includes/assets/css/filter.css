
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Vina+Sans&display=swap');
.twwf_submit{
    display: block;
}
.tww_filter_field_title{
    margin:0px;
    width: 100%;
    font: normal normal bold 14px/20px Open Sans;
    letter-spacing: 0.12px;
    color: #0B0D0D;
}
.tww_filter_field_block{
    display: inline-block;
    width: 100%;
}

.tww_box_checkbox{
    display:none;
}
.tww_box_checkbox_label{
    cursor:pointer;
    padding: 4px 12px;
    border: 1px solid #0b0d0d;
    background: #FFFFFF0D 0% 0% no-repeat padding-box;
    border-radius: 6px;
    font: normal normal normal 12px/18px Open Sans;
    letter-spacing: 0px;
    color: #0B0D0D;
    box-sizing: border-box;
    display: block;
}

.tww_box_checkbox:checked + .tww_box_checkbox_label{
    background: #0B0D0D 0% 0% no-repeat padding-box;
    color: #FFFFFF;
}
.tww_box_item_container{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
}



.tww_price_item{
    font: normal normal 600 12px/18px Open Sans;
    letter-spacing: 0.1px;
    color: #0B0D0D;
    opacity: 0.5;
    margin-top: 17px;
}
.twwf_min_price{
    text-align: left;
    float:left;
}
.twwf_max_price{
    text-align: right;
    float:right;
}
/*price slider*/
.tww_filter_form .tww_filter_field_block .ui-slider-handle{
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 8px #00000026;
    border: 1px solid #F4F4F4;
    opacity: 1;
    border-radius: 50%;
    width:20px;
    height:20px;
    box-sizing: border-box;
    top: -7px;
    outline-width:0px !important;
}
.tww_filter_form .tww_filter_field_block  .ui-slider-handle .tww_handle_price{
    display:inline-block;
    position:absolute;
    top:-20px;
    font: normal normal 600 12px/18px Open Sans;
    letter-spacing: 0.1px;
    color: #0B0D0D;
    opacity: 1;
}
.tww_filter_form .tww_filter_field_block  .ui-slider-handle .tww_handle_price_max.change_top{
    top:-16px;
}
.tww_filter_form .tww_filter_field_block  .ui-slider-handle .tww_handle_price_min.change_top{
    top:-30px;
}
.tww_filter_form .tww_filter_field_block  .ui-slider-handle .tww_handle_price_max{
    right: 0px;
}
.tww_filter_form .tww_filter_field_block  .ui-slider-range{
    background: #0B0D0D 0% 0% no-repeat padding-box;
    border-radius: 6px;
    opacity: 1;
}
.twwf_price_slider.ui-slider{
    height: 6px;
    background: #E4E4E4 0% 0% no-repeat padding-box;
    border-radius: 6px;
    opacity: 1;
    border:0px !important;
}

.twwf_color_list.twwf_horizontal{
    display: flex;
    flex-direction: row;
    column-gap: 20px;
    row-gap: 20px;
    flex-wrap: wrap;
}
.twwf_color_list.twwf_vertical{
    display: flex;
    flex-direction: column;
    grid-gap: 20px;
}
.twwf_color_list.twwf_vertical .twwf_color_checkbox_label{
    display: flex;
    align-items: center;
}
.twwf_color_list.twwf_vertical .twwf_color_checkbox_label .tww_color_name{
    margin-left: 43px;
}
.twwf_color_list.twwf_vertical .twwf_color_checkbox_label::before{
    margin-right: 10px;
}



.twwf_color_checkbox{
    display:none !important;
}
.twwf_filter_color_block_grid{
    text-align: center;
    word-break: break-word;
    white-space: normal;
    width:55px;
}
.twwf_filter_color_block_grid .twwf_color_checkbox_label{
    display: inline-block;
}
.twwf_filter_color_block_grid .tww_color_name{
    display:block;
    text-align: center;
}
.twwf_color_checkbox_label{
    cursor: pointer;
    background-color: #ffffff;
    width:35px;
    height:35px;
    border:0px;
    position: relative;
    border-radius: 50%;
}
.twwf_color_checkbox_label .tww_color_input_flag{
    width: 35px;
    height: 35px;
    display: inline-block;
    border-radius: 50%;
    border: 1px solid #EAEAEA;
    position: absolute;
    top: -1px;
    left: -1px;
}
.twwf_color_checkbox:checked + .twwf_color_checkbox_label .tww_color_input_flag{
    width: 29px;
    height: 29px;
    top: 2px;
    left: 2px;
}
.twwf_color_checkbox:checked + .twwf_color_checkbox_label{
    border: 1px solid #000000;
}

.twwf_checkbox_list{
    display: flex;
    flex-direction: column;
    grid-gap: 10px;
}
.twwf_checkbox_list{
    margin:0px;
    padding: 0px;
}
.twwf_checkbox_list label{
    text-align: left;
    font: normal normal normal 14px/20px Open Sans;
    letter-spacing: 0.12px;
    color: #0B0D0D;
}
.twwf_checkbox_item label{
    margin-left: 9px;
}
.twwf_checkbox_item{
    display: flex;
    flex-direction: row;
    align-items: center;
}


.twwf_checkbox_list .container,.twwf_radio_list .container {
    display: block;
    position: relative;
    /*padding-left: 21px;*/
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font: normal normal normal 14px/20px Open Sans;
    letter-spacing: 0.12px;
    color: #0B0D0D;
    padding-left: 1.5px;
}
.twwf_checkbox_list .container input,.twwf_radio_list .container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}
.twwf_checkbox_list .checkmark,.twwf_radio_list .checkmark {
    position: absolute;
    width: 14px;
    height: 14px;
    background-color: #fff;
    border: 1px solid #0b0d0d1c;
    border-radius: 2px;
    display: inline-block;
    top:3px;
}
.checkbox_field_option_title, .radio_field_option_title{
    margin-left: 22px;
    display: block;
}
.twwf_radio_list .checkmark{
    border-radius: 50% !important;
}
.twwf_radio_list .checkmark::after{
    display: none !important;
    content:'' !important;
}
.twwf_checkbox_list .container:hover input ~ .checkmark,.twwf_radio_list .container:hover input ~ .checkmark {
    background-color: #ccc;
}
.twwf_checkbox_list .container input:checked ~ .checkmark,.twwf_radio_list .container input:checked ~ .checkmark {
    background: #0B0D0D 0% 0% no-repeat padding-box;
}
.twwf_radio_list .container input:checked ~ .checkmark {
    outline: 1px solid #0b0d0d1c !important;
    border: 2px solid #fff !important;
}
.twwf_checkbox_list .checkmark:after,.twwf_radio_list .checkmark:after {
    content: "";
    position: absolute;
    display: none;
}
.twwf_checkbox_list .container input:checked ~ .checkmark:after,.twwf_radio_list .container input:checked ~ .checkmark:after {
    display: block;
}
.twwf_checkbox_list .container .checkmark:after,.twwf_radio_list .container .checkmark:after {
    left: 4px;
    top: 1px;
    width: 5px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(35deg);
    -ms-transform: rotate(35deg);
    transform: rotate(35deg);
}



.tww_filter_form{
    max-height: 750px;
    overflow-y: hidden;
    overflow-x: hidden;
    padding-right: 14px;
}
.twwf_scrollable:hover{
    overflow-y: auto !important;
    padding-right: 7px !important;
}
.twwf_dropdown_list .select2-container{
    width: 100% !important;
}

/* width */
.tww_filter_form::-webkit-scrollbar {
    width: 7px;
}

/* Track */
.tww_filter_form::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
.tww_filter_form::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

/* Handle on hover */
/*.tww_filter_form::-webkit-scrollbar-thumb:hover {
    background: #555;
}*/

.twwf_submit{
    cursor:pointer !important;
}

.twwf_price_slider_container{
    min-height:40px;
    padding: 22px 15px 0px 15px;
}
.twwf_radio_list{
    display: flex;
    flex-direction: column;
    grid-gap: 10px;
}
.tww_filter_form .twwf_dropdown{
    min-height:40px !important;
    padding: 0px 16px 0px 16px;
    box-sizing: border-box;
    background: #FFFFFF0D 0% 0% no-repeat padding-box;
    border: 1px solid #EAEAEA;
    border-radius: 6px;
}
.tww_filter_form .select2-container .select2-selection--single {
    height: 40px !important;
    display: flex;
    align-items: center;
}
.tww_filter_form .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 0px 16px 0px 16px;
    box-sizing: border-box;
}
.tww_filter_form .select2-selection__arrow {
    height: 40px !important;
}
.select2-container .select2-results {
    padding: 5px 6px 12px 6px;
}
.select2-container .select2-dropdown {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #EAEAEA;
    border-radius: 6px;
}
.admin-bar .select2-container .select2-dropdown{
    margin-top: 32px;
}
.tww_filter_form .select2-container--default.select2-container--open.select2-container--below .select2-selection--single{
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #EAEAEA;
    border-radius: 6px;
}
/*.select2-container.select2-container--default .select2-results__option--highlighted[aria-selected] {*/
/*    text-align: left;*/
/*    font: normal normal normal 12px/18px Open Sans;*/
/*    letter-spacing: 0px;*/
/*    color: #0B0D0D;*/
/*    background: #FFFFFF0D 0% 0% no-repeat padding-box;*/
/*    border-radius: 4px;*/
/*}*/
.select2-results ul li{
    text-align: left;
    font: normal normal normal 12px/18px Open Sans;
    letter-spacing: 0px;
    color: #0B0D0D;
}
.select2-container--default .select2-results__option--highlighted[aria-selected]{
    background: #0B0D0D0D 0% 0% no-repeat padding-box;
    border-radius: 4px;
    color: #0B0D0D;
}


.select2-container--default .select2-results__option[aria-selected=true] {
    background: transparent;
    color: #0B0D0D;
    opacity: 0.5;
}
.tww_filter_form .select2-container--default .select2-selection--multiple .select2-selection__choice{
    text-align: center;
    font: normal normal 600 12px/18px Open Sans;
    letter-spacing: 0.1px;
    color: #0B0D0D;
    padding: 2px 8px 2px 8px;
}
.tww_filter_form .select2-selection__choice{
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
}
.tww_filter_form .select2-selection__choice__remove{
    float: right;
    margin-left: 4px;
    white-space: nowrap;
    width: 14px;
    font-size: 20px;
    font-weight: 400;
}
.tww_filter_form .select2-selection--multiple{
    background: #FFFFFF0D 0% 0% no-repeat padding-box;
    border: 1px solid #EAEAEA;
    border-radius: 6px;
}
.twwf_reset_filter{
    text-decoration: underline;
}
.twwf_filter_actions{
    display: inline-block;
    text-align: center;
}
.twwf_filter_actions a{
    text-decoration:underline;
    margin-top: 10px;
    display: block;
    cursor:pointer;
}
.twwf_field_header{
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor:pointer;
    margin-bottom: 20px;
}
.twwf_open_close_field{
    margin-left: auto;
    cursor:pointer;
    width:14px;
    height: 14px;
    background-image: url("../images/dropdown_Icon_black.svg");
    background-repeat: no-repeat;
    background-size: contain;
    right: 8px;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}
.twwf_close{
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
}
.twwf_hide_field, .twwf_hide_field *{
    /*display:none !important;*/
    visibility: hidden !important;
    height: 0px !important;
    margin: 0px !important;
    padding: 0px !important;
    min-height: 0px !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}
.twwf_pillbox_field{
    width: 100% !important;
}
.twwf_filtered_fields{
    margin:0px 0px 20px 0px;
}
.twwf_filtered_fields span, .twwf_filtered_fields a{
    margin-bottom: 4px;
    display: inline-block;
    margin-right: 8px;
}
.twwf_filtered_fields .twwf_filtered_field{
    border: 1px solid #eee;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 3px 26px 3px 8px;
    cursor:pointer;
    position:relative;
}
.twwf_reset_filtered_fields{
    text-decoration: underline;
}
.twwf_filtered_field:after{
    content: '';
    display: inline-block;
    margin-left: 5px;
    height: 14px;
    width: 14px;
    position: absolute;
    top: calc(100% - 22px);
    font-size: 14px !important;
}
.twwf_reset_filtered_fields{
    cursor: pointer;
}
.twwf_reset_filtered_fields:after{
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 5px;
}
.twwf_expand_collapse_filter{
    cursor: pointer;
    text-decoration: underline;
    text-underline-offset: 4px;
    margin-bottom: 20px;
    display: inline-block;
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none; /* Standard syntax */
}
.twwf_expand_filter:after, .twwf_collapse_filter:after{
    content: '';
    height: 12px;
    width: 12px;
    display: inline-block;
    background-size: auto;
    background-position: center;
    margin-left: 7px;
}

form.twwf_form_expand:hover{
    padding-right: 14px !important;
}
.tww_filter_form input.select2-search__field{
    border-style: unset !important;
    padding: 0px !important;
    line-height: 0 !important;
    box-shadow: unset !important;
    text-indent: 10px;
}
.tww_filter_form .select2-container--default.select2-container--focus .select2-selection--multiple,.tww_filter_form .select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
    border-width: 1px !important;
    border-style: solid !important;
}
.twwf_dropdown_list .select2-selection__arrow{
    display:none !important;
}
.twwf_hide_cat{
    display:none !important;
}

.twwf_child_cat_label{
    margin-left: 23px;
}
.twwf_filter_color_block_list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding-left: 1px;
}
.twwf_filter_color_block_list span.tww_color_name {
    display: inline-block;
    width: calc(100% - 46px);
}
.twwf_filter_color_block .tww_color_name{
    cursor: pointer;
}
.tww_filter_form li.select2-selection__choice {
    max-width: 100%;
    overflow: hidden;
    text-wrap: initial;
}
.tww_filter_form ul.select2-selection__rendered {
    padding-right: 12px !important; //overrides select2 style
}
