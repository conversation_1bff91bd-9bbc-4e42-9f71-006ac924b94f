{"type": "composer-plugin", "name": "mcaskill/composer-exclude-files", "description": "Exclude files from autoload_files.php", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "composer-plugin-api": "^1.0 || ^2.0"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "autoload": {"psr-4": {"McAskill\\Composer\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\McAskill\\Composer\\": "tests/"}}, "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "class": "McAskill\\Composer\\ExcludeFilePlugin"}, "config": {"platform": {"php": "5.3.9"}, "platform-check": false}, "scripts": {"test": "simple-phpunit"}, "scripts-descriptions": {"test": "Run all tests"}, "minimum-stability": "dev", "prefer-stable": true}