<?php return array(
    'root' => array(
        'name' => '10web-frontend/10web-manager',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '79c3dbff65d09bca7fd24e86c1b6f9eb3af40356',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '10web-frontend/10web-manager' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '79c3dbff65d09bca7fd24e86c1b6f9eb3af40356',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        '10web-utils/10web-woocommerce-package' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '0cc4edd2a42083937dc0be6a17d6938497c305b4',
            'type' => 'git',
            'install_path' => __DIR__ . '/../10web-utils/10web-woocommerce-package',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        '10web/archive' => array(
            'pretty_version' => 'v2.0.5',
            'version' => '2.0.5.0',
            'reference' => 'f4d1391a5dc30f8a5011466b4249c2a213df3731',
            'type' => 'library',
            'install_path' => __DIR__ . '/../10web/archive',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        '10web/authorization' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '5525b6eeeb21c29276ccc642b01ba32fc62a839a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../10web/authorization',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.4.0',
            'version' => '6.4.0.0',
            'reference' => '4dd1e007f22a927ac77da5a3fbb067b42d3bc224',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kub-at/php-simple-html-dom-parser' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => 'ff22f98bfd9235115c128059076f3eb740d66913',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kub-at/php-simple-html-dom-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mcaskill/composer-exclude-files' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '*******',
            'reference' => '0d481db018d5849e87f87cc3145a2cbf0135e428',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../mcaskill/composer-exclude-files',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nelexa/zip' => array(
            'pretty_version' => '3.3.3',
            'version' => '*******',
            'reference' => '501b52f6fc393a599b44ff348a42740e1eaac7c6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nelexa/zip',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.7.0',
            'version' => '*******',
            'reference' => '52a0d99e69f56b9ec27ace92ba56897fe6993105',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '**********',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.21.1',
            'version' => '********',
            'reference' => 'bb312875dcdd20680419564fe42ba1d9564b9e37',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/archive_tar' => array(
            'pretty_version' => '1.4.14',
            'version' => '1.4.14.0',
            'reference' => '4d761c5334c790e45ef3245f0864b8955c562caa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/archive_tar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/console_getopt' => array(
            'pretty_version' => 'v1.4.3',
            'version' => '1.4.3.0',
            'reference' => 'a41f8d3e668987609178c7c4a9fe48fecac53fa0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/console_getopt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear-core-minimal' => array(
            'pretty_version' => 'v1.10.16',
            'version' => '1.10.16.0',
            'reference' => 'c0f51b45f50683bf5bbf558036854ebc9b54d033',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/pear-core-minimal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear_exception' => array(
            'pretty_version' => 'v1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'b14fbe2ddb0b9f94f5b24cf08783d599f776fff0',
            'type' => 'class',
            'install_path' => __DIR__ . '/../pear/pear_exception',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-stubs/wordpress-stubs' => array(
            'pretty_version' => 'v6.8.2',
            'version' => '6.8.2.0',
            'reference' => '9c8e22e437463197c1ec0d5eaa9ddd4a0eb6d7f8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-stubs/wordpress-stubs',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.46',
            'version' => '3.0.46.0',
            'reference' => '56483a7de62a6c2a6635e42e93b8a9e25d4f0ec6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpstan/phpstan' => array(
            'pretty_version' => '1.12.29',
            'version' => '1.12.29.0',
            'reference' => '0835c625a38ac6484f050077116b6668bc3ab57d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpstan',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rsky/pear-core-min' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v1.10.16',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v4.4.44',
            'version' => '4.4.44.0',
            'reference' => '66bd787edb5e42ff59d3523f623895af05043e4f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.19.0',
            'version' => '1.19.0.0',
            'reference' => 'aed596913b70fae57be53d86faa2e9ef85a2297b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.19.0',
            'version' => '1.19.0.0',
            'reference' => 'b5f7b932ee6fa802fc792eabd77c4c88084517ce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'szepeviktor/phpstan-wordpress' => array(
            'pretty_version' => 'v1.3.5',
            'version' => '1.3.5.0',
            'reference' => '7f8cfe992faa96b6a33bbd75c7bace98864161e7',
            'type' => 'phpstan-extension',
            'install_path' => __DIR__ . '/../szepeviktor/phpstan-wordpress',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
