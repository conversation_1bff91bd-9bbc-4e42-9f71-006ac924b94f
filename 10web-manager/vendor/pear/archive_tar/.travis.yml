sudo: false
language: php
matrix:
  fast_finish: true
  allow_failures:
    - php: nightly
  include:
  - php: 5.2
    dist: precise
  - php: 5.3
    dist: precise
  - php: 5.4
    dist: trusty
  - php: 5.5
    dist: trusty
  - php: 5.6
  - php: 7.0
  - php: 7.1
  - php: 7.2
  - php: 7.3
  - php: 7.4
  - php: nightly
install:
#  - pear upgrade --force --alldeps pear/pear
  - pear install -f package.xml
script:
  - pear version
  - pear run-tests -qr tests/
  - for i in `find tests/ -name '*.out'`; do echo "$i"; cat "$i"; done
